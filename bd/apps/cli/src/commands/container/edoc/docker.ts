/* eslint-disable no-sync */
import { Flags } from "@oclif/core";
import { BaseCommand } from "@core/base";
import * as fs from "fs";
import * as path from "path";

export default class EdocDocker extends BaseCommand<typeof EdocDocker> {
    static description = `Launch local Edoc docker-compose stack`;

    static examples = [
        `$ clara container edoc docker --env development`,
        `$ clara container edoc docker --env production`,
    ];

    static override flags = {
        env: Flags.string({
            description: "Environment",
            required: false,
            default: "development",
            options: ["development", "testing", "staging", "production"],
        }),
        "no-cache": Flags.boolean({
            description: "Build without using cache",
            required: false,
            default: false,
        }),
    };

    public async process(): Promise<object> {
        const { env, "no-cache": noCache } = this.flags;

        this.app.info(`Starting Edoc container in ${env} mode...`);

        try {
            await this.runDocker(env, noCache);
            return {
                message: `Edoc container started successfully`,
                env,
            };
        } catch (error) {
            this.app.fatal(`Failed to start Edoc container: ${error}`);
            return {
                error: error instanceof Error ? error.message : String(error),
            };
        }
    }

    private async runDocker(env: string, noCache: boolean): Promise<void> {
        // Set paths
        const pkgPath = "packages/containers/edoc";
        const fullPkgPath = this.app.repoPath(pkgPath);
        const dockerComposePath = this.app.repoPath(
            `${pkgPath}/docker-compose.yml`
        );
        const tmpEnvPath = path.join(fullPkgPath, ".env");

        try {
            // Create simple .env file with NODE_ENV
            this.app.info("Creating environment file...");
            fs.writeFileSync(tmpEnvPath, `NODE_ENV=${env}\n`);

            // Common docker compose parameters
            const dockerComposeParams = `docker compose -f ${dockerComposePath} --env-file "${tmpEnvPath}" --project-name "edoc"`;

            // Run docker compose
            this.app.info(
                `Starting docker compose with${noCache ? "out" : ""} cache...`
            );
            process.env.DOCKER_BUILDKIT = "1";
            process.env.BUILDKIT_PROGRESS = "plain";
            process.env.NODE_ENV = env;

            if (noCache) {
                this.app.$exec(`${dockerComposeParams} build --no-cache edoc`);
                this.app.$exec(`${dockerComposeParams} up -d edoc`);
            } else {
                this.app.$exec(`${dockerComposeParams} up --build -d edoc`);
            }

            this.app.info("Docker containers started successfully");
        } catch (error) {
            this.app.fatal(`Failed to start docker containers: ${error}`);
        } finally {
            // Clean up temp env file
            if (this.app.isFile(tmpEnvPath)) {
                fs.unlinkSync(tmpEnvPath);
            }
        }
    }
}
